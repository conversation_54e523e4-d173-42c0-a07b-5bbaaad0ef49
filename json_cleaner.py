#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON文件清理工具
用于清理证券时报网站抓取的JSON文件，删除无用信息，保留有用内容
"""

import json
import os
import re
import glob
from typing import Dict, Any, List
import argparse
from datetime import datetime


class JSONCleaner:
    """JSON文件清理器"""
    
    def __init__(self):
        self.processed_count = 0
        self.error_count = 0
        
    def clean_markdown_content(self, markdown_text: str) -> str:
        """
        清理markdown内容，删除无用信息
        
        Args:
            markdown_text: 原始markdown文本
            
        Returns:
            清理后的markdown文本
        """
        if not markdown_text:
            return ""
            
        # 删除图片链接 ![](...)
        markdown_text = re.sub(r'!\[.*?\]\([^)]*\)', '', markdown_text)
        
        # 删除Base64图片 ![Scan me!](<Base64-Image-Removed>)
        markdown_text = re.sub(r'!\[.*?\]\(<Base64-Image-Removed>\)', '', markdown_text)
        
        # 删除导航栏链接（通常在开头）
        # 删除网站logo和导航链接
        markdown_text = re.sub(r'\[!\[.*?\]\(.*?\)\]\(.*?\)', '', markdown_text)
        
        # 删除导航菜单项（- [首页]... 等）
        lines = markdown_text.split('\n')
        cleaned_lines = []
        skip_navigation = False
        
        for line in lines:
            # 检测导航区域开始
            if '- [首页]' in line or '- [快讯]' in line:
                skip_navigation = True
                continue
                
            # 检测导航区域结束（通常是空行后的内容）
            if skip_navigation and line.strip() == "":
                skip_navigation = False
                continue
                
            if skip_navigation:
                continue
                
            # 删除页面底部信息
            if any(keyword in line for keyword in [
                '关于我们', '服务条例', '联系我们', '版权声明', 
                '备案号', '违法和不良信息举报', '深圳证券时报社',
                'Copyright ©', '请完成安全验证', '请拖动滑块'
            ]):
                continue
                
            # 删除推荐文章区域
            if '为你推荐' in line or '时报热榜' in line or '热点视频' in line:
                break
                
            # 删除社交分享相关
            if any(keyword in line for keyword in [
                '微信扫一扫', '点赞', '分享', '网友评论', 
                '登录后可以发言', '发送', '暂无评论'
            ]):
                continue
                
            # 删除位置导航
            if '您当前的位置：' in line:
                continue
                
            # 删除公众号二维码相关
            if '公众号' in line and len(line.strip()) < 10:
                continue
                
            # 保留有用内容
            cleaned_lines.append(line)
        
        # 重新组合文本
        cleaned_text = '\n'.join(cleaned_lines)
        
        # 删除多余的空行
        cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
        
        # 删除开头和结尾的空白
        cleaned_text = cleaned_text.strip()
        
        return cleaned_text
    
    def extract_article_info(self, markdown_text: str) -> Dict[str, str]:
        """
        从markdown中提取文章关键信息
        
        Args:
            markdown_text: markdown文本
            
        Returns:
            包含文章信息的字典
        """
        info = {
            'title': '',
            'source': '',
            'author': '',
            'publish_date': '',
            'content': ''
        }
        
        lines = markdown_text.split('\n')
        content_lines = []
        found_title = False
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # 提取标题（通常是第一个非空的较长行）
            if not found_title and len(line) > 10 and not line.startswith('['):
                info['title'] = line
                found_title = True
                continue
                
            # 提取来源和日期信息
            if '来源：' in line:
                # 匹配格式如：来源：中国基金报2025-03-01 08:04
                match = re.search(r'来源：(.+?)(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2})', line)
                if match:
                    info['source'] = match.group(1).strip()
                    info['publish_date'] = match.group(2).strip()
                else:
                    info['source'] = line.replace('来源：', '').strip()
                continue
                
            # 提取作者信息
            if '记者' in line or '作者：' in line:
                info['author'] = line.strip()
                continue
                
            # 收集正文内容
            if found_title and line:
                content_lines.append(line)
        
        info['content'] = '\n'.join(content_lines)
        return info
    
    def clean_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        清理metadata，只保留有用信息
        
        Args:
            metadata: 原始metadata
            
        Returns:
            清理后的metadata
        """
        useful_fields = [
            'title', 'description', 'keywords', 'language',
            'sourceURL', 'url', 'statusCode', 'contentType'
        ]
        
        cleaned_metadata = {}
        for field in useful_fields:
            if field in metadata:
                cleaned_metadata[field] = metadata[field]
                
        return cleaned_metadata
    
    def process_json_file(self, file_path: str, output_dir: str = None) -> bool:
        """
        处理单个JSON文件
        
        Args:
            file_path: 输入文件路径
            output_dir: 输出目录，如果为None则覆盖原文件
            
        Returns:
            处理是否成功
        """
        try:
            # 读取JSON文件
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 清理markdown内容
            if 'markdown' in data:
                cleaned_markdown = self.clean_markdown_content(data['markdown'])
                
                # 提取文章信息
                article_info = self.extract_article_info(cleaned_markdown)
                
                # 构建清理后的数据结构
                cleaned_data = {
                    'article': article_info,
                    'cleaned_content': cleaned_markdown
                }
                
                # 保留清理后的metadata
                if 'metadata' in data:
                    cleaned_data['metadata'] = self.clean_metadata(data['metadata'])
            else:
                print(f"警告: {file_path} 中没有找到markdown字段")
                return False
            
            # 确定输出路径
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
                filename = os.path.basename(file_path)
                name, ext = os.path.splitext(filename)
                output_path = os.path.join(output_dir, f"{name}_cleaned{ext}")
            else:
                output_path = file_path
            
            # 写入清理后的数据
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(cleaned_data, f, ensure_ascii=False, indent=2)
            
            self.processed_count += 1
            print(f"✓ 已处理: {file_path}")
            return True
            
        except Exception as e:
            self.error_count += 1
            print(f"✗ 处理失败 {file_path}: {str(e)}")
            return False
    
    def process_directory(self, directory: str, output_dir: str = None, pattern: str = "*.json"):
        """
        处理目录中的所有JSON文件
        
        Args:
            directory: 输入目录
            output_dir: 输出目录
            pattern: 文件匹配模式
        """
        json_files = glob.glob(os.path.join(directory, pattern))
        
        if not json_files:
            print(f"在目录 {directory} 中没有找到匹配 {pattern} 的文件")
            return
        
        print(f"找到 {len(json_files)} 个JSON文件")
        print("开始处理...")
        
        for file_path in json_files:
            self.process_json_file(file_path, output_dir)
        
        print(f"\n处理完成!")
        print(f"成功处理: {self.processed_count} 个文件")
        print(f"处理失败: {self.error_count} 个文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='清理JSON文件中的无用信息')
    parser.add_argument('input_path', help='输入文件或目录路径')
    parser.add_argument('-o', '--output', help='输出目录（可选，默认覆盖原文件）')
    parser.add_argument('-p', '--pattern', default='*.json', help='文件匹配模式（默认: *.json）')
    
    args = parser.parse_args()
    
    cleaner = JSONCleaner()
    
    if os.path.isfile(args.input_path):
        # 处理单个文件
        cleaner.process_json_file(args.input_path, args.output)
    elif os.path.isdir(args.input_path):
        # 处理目录
        cleaner.process_directory(args.input_path, args.output, args.pattern)
    else:
        print(f"错误: 路径 {args.input_path} 不存在")


if __name__ == "__main__":
    # 如果直接运行脚本，处理当前目录下的所有JSON文件
    if len(os.sys.argv) == 1:
        print("JSON文件清理工具")
        print("正在处理当前目录下的所有JSON文件...")
        
        cleaner = JSONCleaner()
        cleaner.process_directory(".", "cleaned_output")
    else:
        main()
